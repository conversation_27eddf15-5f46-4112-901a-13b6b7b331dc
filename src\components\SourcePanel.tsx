import React, { useState, useEffect, useMemo } from 'react';
import { Database, Server, FileText, BarChart3, Info, Wifi, WifiOff, Plus, Minus } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useQuery } from '../context/QueryContext';
import { useToast } from '@/hooks/use-toast';
import ResultsTable from './ResultsTable';
import { databaseService } from '../services/databaseService';

const SourcePanel = () => {
  const {
    sourceServer,
    setSourceServer,
    sourceDatabase,
    setSourceDatabase,
    sourceQuery,
    setSourceQuery,
    sourceResults,
    isSourceLoading,
    comparison,
    isSourceConnected,
    setIsSourceConnected,
    isSourceAccordionCollapsed,
    setIsSourceAccordionCollapsed,
    destinationResults,
  } = useQuery();

  const [filters, setFilters] = useState<Record<string, string>>({});
  const [isQueryResultsCollapsed, setIsQueryResultsCollapsed] = useState(false);
  const [isFiltersCollapsed, setIsFiltersCollapsed] = useState(true);
  const [availableDatabases, setAvailableDatabases] = useState<string[]>([]);
  const [serverInfo, setServerInfo] = useState<{ name: string; alias: string; user: string } | null>(null);
  const [isLoadingServerInfo, setIsLoadingServerInfo] = useState(true);
  const { toast } = useToast();

  // Load server info and databases on component mount
  useEffect(() => {
    const loadServerInfo = async () => {
      try {
        setIsLoadingServerInfo(true);
        console.log('Loading server info...');
        const info = await databaseService.getServerInfo();
        console.log('Server info loaded:', info);
        setServerInfo(info);
        setSourceServer(info.alias); // Set the server alias as default
      } catch (error) {
        console.error('Failed to load server info:', error);
        toast({
          title: "Server Loading Failed",
          description: "Could not retrieve server information.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingServerInfo(false);
      }
    };

    loadServerInfo();
  }, [setSourceServer, toast]);

  // Load databases when server is selected
  useEffect(() => {
    const loadDatabases = async () => {
      if (sourceServer && serverInfo) {
        try {
          const databases = await databaseService.getDatabases();
          setAvailableDatabases(databases);
        } catch (error) {
          console.error('Failed to load databases:', error);
          toast({
            title: "Database Loading Failed",
            description: "Could not retrieve database list from server.",
            variant: "destructive",
          });
        }
      }
    };

    loadDatabases();
  }, [sourceServer, serverInfo, toast]);

  const handleConnectionToggle = async (checked: boolean) => {
    if (checked) {
      if (!sourceServer || !sourceDatabase) {
        toast({
          title: "Connection Failed",
          description: "Please select server and database first.",
          variant: "destructive",
        });
        return;
      }

      try {
        const connectionResult = await databaseService.testConnection();
        if (connectionResult.success) {
          setIsSourceConnected(true);
          toast({
            title: "Connection Successful",
            description: `Connected to ${sourceServer}/${sourceDatabase}`,
          });
        } else {
          throw new Error(connectionResult.message || 'Connection failed');
        }
      } catch (error) {
        toast({
          title: "Connection Failed",
          description: error instanceof Error ? error.message : 'Unknown error occurred',
          variant: "destructive",
        });
      }
    } else {
      setIsSourceConnected(false);
      toast({
        title: "Disconnected",
        description: "Source database connection closed.",
      });
    }
  };

  const handleFilterChange = (column: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [column]: value
    }));
  };

  const getFilteredResults = () => {
    if (!sourceResults?.data) return sourceResults;
    
    const filteredData = sourceResults.data.filter(row => {
      return Object.entries(filters).every(([column, filterValue]) => {
        if (!filterValue) return true;
        return String(row[column]).toLowerCase().includes(filterValue.toLowerCase());
      });
    });

    return {
      ...sourceResults,
      data: filteredData,
      totalRows: filteredData.length
    };
  };

  const columns = sourceResults?.data.length > 0 ? Object.keys(sourceResults.data[0]) : [];

  // Memoized comparison statistics
  const comparisonStats = useMemo(() => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return { matchedRows: 0, unmatchedRows: 0 };
    }

    let matchedRows = 0;
    let unmatchedRows = 0;
    const maxRows = Math.max(sourceResults.data.length, destinationResults.data.length);

    for (let i = 0; i < maxRows; i++) {
      const sourceRow = sourceResults.data[i];
      const destinationRow = destinationResults.data[i];

      if (!sourceRow || !destinationRow) {
        unmatchedRows++;
      } else {
        const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
        if (isMatch) {
          matchedRows++;
        } else {
          unmatchedRows++;
        }
      }
    }

    return { matchedRows, unmatchedRows };
  }, [comparison, sourceResults, destinationResults]);

  return (
    <div className="h-full flex flex-col bg-gradient-to-b from-blue-50 to-indigo-50 shadow-lg shadow-black/50 rounded-lg overflow-hidden">
      <Collapsible open={!isSourceAccordionCollapsed} onOpenChange={(open) => setIsSourceAccordionCollapsed(!open)}>
        <div className="p-3 sm:p-4 lg:p-5 border-b-2 border-blue-200 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-t-lg">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              {/* Connection Status Indicator */}
              {isSourceConnected ? (
                <Wifi className="w-4 h-4 text-green-600" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-600" />
              )}
              <div className="p-2 bg-blue-600 rounded-full">
                <Database className="w-3 h-3 sm:w-4 sm:h-4 lg:w-6 lg:h-6 text-white" />
              </div>
              <h2 className="text-base sm:text-lg lg:text-xl font-bold text-blue-900">
                Source Activities
              </h2>
              <CollapsibleTrigger asChild>
                <button className="p-1 hover:bg-blue-200 rounded-full transition-colors">
                  {isSourceAccordionCollapsed ? (
                    <Plus className="w-4 h-4 text-blue-600" />
                  ) : (
                    <Minus className="w-4 h-4 text-blue-600" />
                  )}
                </button>
              </CollapsibleTrigger>
            </div>
            
            <div className="flex items-center gap-3">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-blue-800">
                        {isSourceConnected ? 'Disconnect' : 'Connect'}
                      </span>
                      <Switch
                        checked={isSourceConnected}
                        onCheckedChange={handleConnectionToggle}
                        className="data-[state=checked]:bg-blue-600"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isSourceConnected ? 'Disconnect from server' : 'Connect to server'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>


          
          <CollapsibleContent>
            <div className="space-y-6">
              {/* Desktop: Grid layout, Mobile/Tablet: Stacked layout */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Label htmlFor="source-server" className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                          <Server className="w-4 h-4" />
                          Select Source Server
                          <Info className="w-3 h-3 text-blue-600 cursor-help" />
                        </Label>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Choose the server where your source data is located</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Select value={sourceServer} onValueChange={setSourceServer} disabled={isLoadingServerInfo}>
                    <SelectTrigger className="border-2 border-blue-300 focus:border-blue-500 bg-white shadow-md">
                      <SelectValue placeholder={isLoadingServerInfo ? "Loading servers..." : "Choose server..."} />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingServerInfo ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 animate-spin rounded-full border-2 border-blue-600 border-t-transparent"></div>
                            Loading...
                          </div>
                        </SelectItem>
                      ) : serverInfo ? (
                        <SelectItem key={serverInfo.alias} value={serverInfo.alias}>
                          <div className="flex items-center gap-2">
                            <Server className="w-4 h-4 text-blue-600" />
                            {serverInfo.alias} ({serverInfo.name})
                          </div>
                        </SelectItem>
                      ) : (
                        <SelectItem value="no-server" disabled>
                          <div className="flex items-center gap-2 text-red-600">
                            <Server className="w-4 h-4" />
                            No server available
                          </div>
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Label htmlFor="source-database" className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                          <Database className="w-4 h-4" />
                          Select Database
                          <Info className="w-3 h-3 text-blue-600 cursor-help" />
                        </Label>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Select the specific database to query from</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Select value={sourceDatabase} onValueChange={setSourceDatabase} disabled={!sourceServer}>
                    <SelectTrigger className="border-2 border-blue-300 focus:border-blue-500 bg-white shadow-md">
                      <SelectValue placeholder="Choose database..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableDatabases.map((database) => (
                        <SelectItem key={database} value={database}>
                          <div className="flex items-center gap-2">
                            <Database className="w-4 h-4 text-blue-600" />
                            {database}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Label htmlFor="source-query" className="text-sm font-semibold text-blue-800 flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        SQL Query
                        <Info className="w-3 h-3 text-blue-600 cursor-help" />
                      </Label>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Enter your SQL query to execute on the source database</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <Textarea
                  id="source-query"
                  value={sourceQuery}
                  onChange={(e) => setSourceQuery(e.target.value)}
                  placeholder="SELECT * FROM table_name WHERE condition..."
                  className="mt-2 h-32 sm:h-40 font-mono text-sm resize-none border-2 border-blue-300 focus:border-blue-500 bg-white shadow-md"
                  disabled={!isSourceConnected}
                />
              </div>
            </div>
          </CollapsibleContent>
        </div>
      </Collapsible>
      
      <div className="flex-1 p-3 sm:p-4 lg:p-5 overflow-hidden bg-white rounded-b-lg min-h-0">
        <Collapsible open={!isQueryResultsCollapsed} onOpenChange={(open) => setIsQueryResultsCollapsed(!open)}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4 flex-wrap">
              <div className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-blue-600" />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <h3 className="text-base sm:text-lg font-semibold text-blue-900 flex items-center gap-2">
                        Query Results
                        <Info className="w-3 h-3 text-blue-600 cursor-help" />
                      </h3>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Results from executing your source query</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {/* Statistics */}
              {sourceResults && (
                <div className="flex items-center gap-4 flex-wrap">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium text-slate-700">
                      Total Rows: {sourceResults.data.length.toLocaleString()}
                    </span>
                  </div>

                  {comparison === 'completed' && (
                    <>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium text-green-700">
                          Matched: {comparisonStats.matchedRows.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span className="text-sm font-medium text-red-700">
                          Unmatched: {comparisonStats.unmatchedRows.toLocaleString()}
                        </span>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
            <CollapsibleTrigger asChild>
              <button className="p-1 hover:bg-blue-200 rounded-full transition-colors">
                {isQueryResultsCollapsed ? (
                  <Plus className="w-4 h-4 text-blue-600" />
                ) : (
                  <Minus className="w-4 h-4 text-blue-600" />
                )}
              </button>
            </CollapsibleTrigger>
          </div>

          <CollapsibleContent>
            {/* Collapsible Filters */}
            {sourceResults && columns.length > 0 && (
              <Collapsible open={!isFiltersCollapsed} onOpenChange={(open) => setIsFiltersCollapsed(!open)}>
                <div className="mb-4">
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                    <h4 className="text-sm font-semibold text-blue-800">Filter Results</h4>
                    <CollapsibleTrigger asChild>
                      <button className="p-1 hover:bg-blue-200 rounded-full transition-colors">
                        {isFiltersCollapsed ? (
                          <Plus className="w-4 h-4 text-blue-600" />
                        ) : (
                          <Minus className="w-4 h-4 text-blue-600" />
                        )}
                      </button>
                    </CollapsibleTrigger>
                  </div>
                  <CollapsibleContent>
                    <div className="mt-2 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        {columns.map((column) => (
                          <div key={column} className="space-y-1">
                            <Label className="text-xs font-medium text-blue-700">{column}</Label>
                            <input
                              type="text"
                              placeholder={`Filter by ${column}...`}
                              value={filters[column] || ''}
                              onChange={(e) => handleFilterChange(column, e.target.value)}
                              className="w-full px-3 py-2 text-sm border border-blue-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                            />
                          </div>
                        ))}
                      </div>
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            )}

            <ResultsTable 
              results={getFilteredResults()} 
              loading={isSourceLoading} 
              comparison={comparison}
              type="source"
            />
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
};

export default SourcePanel;
