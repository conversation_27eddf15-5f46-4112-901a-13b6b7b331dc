
import React, { useState, useMemo, useCallback, useRef } from 'react';
import { Loader2, <PERSON>Tex<PERSON>, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { useQuery } from '../context/QueryContext';

interface ResultsTableProps {
  results: {
    data: Record<string, any>[];
    totalRows: number;
    displayedRows?: number;
    isLimited?: boolean;
  } | null;
  loading: boolean;
  comparison: 'none' | 'completed';
  type: 'source' | 'destination';
}

const ResultsTable = ({ results, loading, comparison, type }: ResultsTableProps) => {
  const { sourceResults, destinationResults } = useQuery();

  // No pagination - show all data

  // Column width state
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [isResizing, setIsResizing] = useState<string | null>(null);
  const tableRef = useRef<HTMLDivElement>(null);
  const scrollbarRef = useRef<HTMLDivElement>(null);
  const resizeStartX = useRef<number>(0);
  const resizeStartWidth = useRef<number>(0);

  // Sorting state
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // Sorting functionality
  const sortedData = useMemo(() => {
    if (!results?.data || !sortConfig) return results?.data || [];

    return [...results.data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;
      if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;

      // Convert to strings for comparison
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();

      // Try to parse as numbers for numeric sorting
      const aNum = parseFloat(aStr);
      const bNum = parseFloat(bStr);

      if (!isNaN(aNum) && !isNaN(bNum)) {
        return sortConfig.direction === 'asc' ? aNum - bNum : bNum - aNum;
      }

      // String comparison
      if (aStr < bStr) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aStr > bStr) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [results?.data, sortConfig]);

  // Show all data without pagination
  const allData = useMemo(() => {
    if (!sortedData) return [];
    return sortedData;
  }, [sortedData]);

  // Memoized comparison statistics
  const comparisonStats = useMemo(() => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return { matchedRows: 0, unmatchedRows: 0 };
    }

    let matchedRows = 0;
    let unmatchedRows = 0;
    const maxRows = Math.max(sourceResults.data.length, destinationResults.data.length);

    for (let i = 0; i < maxRows; i++) {
      const sourceRow = sourceResults.data[i];
      const destinationRow = destinationResults.data[i];

      if (!sourceRow || !destinationRow) {
        unmatchedRows++;
      } else {
        const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
        if (isMatch) {
          matchedRows++;
        } else {
          unmatchedRows++;
        }
      }
    }

    return { matchedRows, unmatchedRows };
  }, [comparison, sourceResults, destinationResults]);

  const getComparisonClass = (globalRowIndex: number) => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return '';
    }

    const sourceRow = sourceResults.data[globalRowIndex];
    const destinationRow = destinationResults.data[globalRowIndex];

    if (!sourceRow || !destinationRow) {
      return 'bg-red-100 border-red-300';
    }

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    // Use subtle background colors so cell-level differences stand out more
    return isMatch ? 'bg-green-50 border-green-200' : 'bg-orange-50 border-orange-200';
  };

  // Enhanced function to get cell-level comparison class for different values
  const getCellComparisonClass = (globalRowIndex: number, columnName: string, cellValue: any) => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return '';
    }

    const sourceRow = sourceResults.data[globalRowIndex];
    const destinationRow = destinationResults.data[globalRowIndex];

    // If either row is missing, highlight all cells in that row
    if (!sourceRow || !destinationRow) {
      return 'text-red-600 font-bold bg-red-50';
    }

    // Check if the entire row matches first
    const isRowMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    if (isRowMatch) {
      return ''; // No special styling for completely matched rows
    }

    // For unmatched rows, compare this specific cell value with the corresponding cell in the other table
    const otherResults = type === 'source' ? destinationResults : sourceResults;
    const otherRow = otherResults.data[globalRowIndex];

    if (!otherRow) {
      return 'text-red-600 font-bold bg-red-50';
    }

    // Get the corresponding cell value from the other table
    const currentValue = cellValue;
    const otherValue = otherRow[columnName];

    // Normalize values for comparison (handle null, undefined, empty strings)
    const normalizeValue = (val: any) => {
      if (val === null || val === undefined) return null;
      if (typeof val === 'string' && val.trim() === '') return '';
      return val;
    };

    const normalizedCurrent = normalizeValue(currentValue);
    const normalizedOther = normalizeValue(otherValue);

    // Compare the specific cell values - only highlight if they're different
    const isCellMatch = JSON.stringify(normalizedCurrent) === JSON.stringify(normalizedOther);

    // Return red styling only for cells that are actually different
    return isCellMatch ? '' : 'text-red-600 font-bold bg-red-50';
  };



  // Sorting handler
  const handleSort = useCallback((columnKey: string) => {
    setSortConfig(prevConfig => {
      if (prevConfig?.key === columnKey) {
        // Toggle direction or clear sort
        if (prevConfig.direction === 'asc') {
          return { key: columnKey, direction: 'desc' };
        } else {
          return null; // Clear sort
        }
      } else {
        // New column sort
        return { key: columnKey, direction: 'asc' };
      }
    });
  }, []);

  // Auto-resize column to fit content
  const autoResizeColumn = useCallback((columnName: string) => {
    if (!results?.data || results.data.length === 0) return;

    // Create a temporary element to measure text width
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return;

    context.font = '14px system-ui, -apple-system, sans-serif'; // Match table font

    // Measure header width
    let maxWidth = context.measureText(columnName).width + 60; // Add padding for sort icon

    // Measure content width (sample first 100 rows for performance)
    const sampleSize = Math.min(100, results.data.length);
    for (let i = 0; i < sampleSize; i++) {
      const cellValue = String(results.data[i][columnName] || '');
      const textWidth = context.measureText(cellValue).width + 32; // Add padding
      maxWidth = Math.max(maxWidth, textWidth);
    }

    // Set reasonable bounds
    const finalWidth = Math.max(80, Math.min(400, maxWidth));

    setColumnWidths(prev => ({
      ...prev,
      [columnName]: finalWidth
    }));
  }, [results?.data]);

  // Column resizing handlers
  const handleMouseDown = useCallback((e: React.MouseEvent, columnName: string) => {
    e.preventDefault();
    setIsResizing(columnName);
    resizeStartX.current = e.clientX;
    resizeStartWidth.current = columnWidths[columnName] || 150;
  }, [columnWidths]);

  // Handle double-click for auto-resize
  const handleDoubleClick = useCallback((columnName: string) => {
    autoResizeColumn(columnName);
  }, [autoResizeColumn]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;

    const deltaX = e.clientX - resizeStartX.current;
    const newWidth = Math.max(80, resizeStartWidth.current + deltaX); // Minimum width of 80px

    setColumnWidths(prev => ({
      ...prev,
      [isResizing]: newWidth
    }));
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(null);
  }, []);

  // Add global mouse event listeners for column resizing
  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Reset column widths when results change
  React.useEffect(() => {
    setColumnWidths({}); // Reset column widths when results change
  }, [results]);

  // Sync scrolling between table and fixed scrollbar
  React.useEffect(() => {
    const tableElement = tableRef.current;
    const scrollbarElement = scrollbarRef.current;

    if (!tableElement || !scrollbarElement) return;

    const handleTableScroll = () => {
      scrollbarElement.scrollLeft = tableElement.scrollLeft;
    };

    tableElement.addEventListener('scroll', handleTableScroll);

    return () => {
      tableElement.removeEventListener('scroll', handleTableScroll);
    };
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-sm text-slate-600">Executing query...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 text-slate-400" />
          <p className="text-sm text-slate-600">No results to display</p>
          <p className="text-xs text-slate-400 mt-1">Execute a query to see results</p>
        </div>
      </div>
    );
  }

  const columns = results.data.length > 0 ? Object.keys(results.data[0]) : [];

  return (
    <div className="flex flex-col h-full space-y-4 min-h-0">



      {/* Results Table */}
      <div className="flex-1 border border-slate-200 rounded-lg flex flex-col min-h-0 relative">
        {/* Table Container */}
        <div className="flex-1 min-h-0 relative">
          <div
            ref={tableRef}
            className="result-table-scroll-container h-full w-full"
            style={{
              minHeight: '300px',
              maxHeight: '100%'
            }}
          >
            <Table className="w-full" style={{ minWidth: `${Math.max(columns.length * 150, 800)}px` }}>
              <TableHeader className="sticky top-0 z-10 bg-slate-100 border-b-2 border-slate-300">
                <TableRow>
                  {columns.map((column, index) => {
                    const columnWidth = columnWidths[column] || (index === 0 ? 200 : 150);
                    const isSorted = sortConfig?.key === column;
                    const sortDirection = isSorted ? sortConfig.direction : null;

                    return (
                      <TableHead
                        key={column}
                        className="font-semibold text-slate-800 border-r border-slate-200 last:border-r-0 px-4 py-3 text-left whitespace-nowrap bg-slate-100 relative group cursor-pointer hover:bg-slate-200 transition-colors"
                        style={{
                          width: columnWidth,
                          minWidth: columnWidth,
                          maxWidth: columnWidth
                        }}
                        onClick={() => handleSort(column)}
                      >
                        <div className="flex items-center justify-between pr-6" title={`${column} - Click to sort, double-click resize handle to auto-fit`}>
                          <span className="truncate">{column}</span>
                          <div className="flex items-center ml-2">
                            {/* Sort Icon */}
                            {isSorted ? (
                              sortDirection === 'asc' ? (
                                <ArrowUp className="w-4 h-4 text-blue-600" />
                              ) : (
                                <ArrowDown className="w-4 h-4 text-blue-600" />
                              )
                            ) : (
                              <ArrowUpDown className="w-4 h-4 text-slate-400 group-hover:text-slate-600" />
                            )}
                          </div>
                        </div>
                        {/* Column Resize Handle */}
                        <div
                          className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize bg-transparent hover:bg-blue-400 group-hover:bg-blue-300 transition-colors"
                          onMouseDown={(e) => {
                            e.stopPropagation(); // Prevent sort when resizing
                            handleMouseDown(e, column);
                          }}
                          onDoubleClick={(e) => {
                            e.stopPropagation(); // Prevent sort when auto-resizing
                            handleDoubleClick(column);
                          }}
                          style={{
                            right: '-2px',
                            width: '4px',
                            zIndex: 20
                          }}
                          title="Drag to resize, double-click to auto-fit"
                        />
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {allData.map((row, index) => {
                  const globalIndex = index;
                  return (
                    <TableRow
                      key={globalIndex}
                      className={`table-row-optimized hover:bg-slate-50 transition-colors border-b border-slate-100 ${getComparisonClass(globalIndex)}`}
                    >
                      {columns.map((column, colIndex) => {
                        const cellValue = row[column];
                        const displayValue = cellValue !== null && cellValue !== undefined ? String(cellValue) : '';
                        const columnWidth = columnWidths[column] || (colIndex === 0 ? 200 : 150);

                        const cellComparisonClass = getCellComparisonClass(globalIndex, column, cellValue);

                        return (
                          <TableCell
                            key={column}
                            className={`border-r border-slate-100 last:border-r-0 text-sm px-4 py-2 whitespace-nowrap overflow-hidden text-ellipsis ${cellComparisonClass}`}
                            style={{
                              width: columnWidth,
                              minWidth: columnWidth,
                              maxWidth: columnWidth
                            }}
                            title={displayValue} // Show full value on hover
                          >
                            <div className="truncate">
                              {displayValue}
                            </div>
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Fixed Horizontal Scrollbar - Always Visible for Result Set */}
        <div
          ref={scrollbarRef}
          className="w-full bg-white border-t border-slate-200 z-20"
          style={{
            height: '17px',
            overflowX: 'scroll', // Always show horizontal scrollbar
            overflowY: 'hidden',
            scrollbarWidth: 'thin',
            scrollbarColor: '#cbd5e1 #f1f5f9',
            display: 'block' // Force display even when content fits
          }}
          onScroll={(e) => {
            if (tableRef.current) {
              tableRef.current.scrollLeft = e.currentTarget.scrollLeft;
            }
          }}
        >
          <div style={{ width: `${Math.max(columns.length * 150, 800)}px`, height: '1px' }}></div>
        </div>
      </div>


    </div>
  );
};

export default ResultsTable;
