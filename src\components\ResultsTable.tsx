
import React, { useState, useMemo, useCallback, useRef } from 'react';
import { Loader2, FileText, ChevronLeft, ChevronRight, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { useQuery } from '../context/QueryContext';

interface ResultsTableProps {
  results: {
    data: Record<string, any>[];
    totalRows: number;
    displayedRows?: number;
    isLimited?: boolean;
  } | null;
  loading: boolean;
  comparison: 'none' | 'completed';
  type: 'source' | 'destination';
}

const ResultsTable = ({ results, loading, comparison, type }: ResultsTableProps) => {
  const { sourceResults, destinationResults } = useQuery();

  // Pagination state - Increased to 5000 rows per page for better performance with 100k records
  const [currentPage, setCurrentPage] = useState(1);
  const rowsPerPage = 5000; // Show 5000 rows per page for better performance with large datasets

  // Column width state
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [isResizing, setIsResizing] = useState<string | null>(null);
  const tableRef = useRef<HTMLDivElement>(null);
  const resizeStartX = useRef<number>(0);
  const resizeStartWidth = useRef<number>(0);

  // Sorting state
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  } | null>(null);

  // Sorting functionality
  const sortedData = useMemo(() => {
    if (!results?.data || !sortConfig) return results?.data || [];

    return [...results.data].sort((a, b) => {
      const aValue = a[sortConfig.key];
      const bValue = b[sortConfig.key];

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortConfig.direction === 'asc' ? -1 : 1;
      if (bValue == null) return sortConfig.direction === 'asc' ? 1 : -1;

      // Convert to strings for comparison
      const aStr = String(aValue).toLowerCase();
      const bStr = String(bValue).toLowerCase();

      // Try to parse as numbers for numeric sorting
      const aNum = parseFloat(aStr);
      const bNum = parseFloat(bStr);

      if (!isNaN(aNum) && !isNaN(bNum)) {
        return sortConfig.direction === 'asc' ? aNum - bNum : bNum - aNum;
      }

      // String comparison
      if (aStr < bStr) return sortConfig.direction === 'asc' ? -1 : 1;
      if (aStr > bStr) return sortConfig.direction === 'asc' ? 1 : -1;
      return 0;
    });
  }, [results?.data, sortConfig]);

  // Memoized pagination calculations
  const paginationData = useMemo(() => {
    if (!sortedData) return { paginatedData: [], totalPages: 0, startRow: 0, endRow: 0 };

    const totalPages = Math.ceil(sortedData.length / rowsPerPage);
    const startIndex = (currentPage - 1) * rowsPerPage;
    const endIndex = Math.min(startIndex + rowsPerPage, sortedData.length);
    const paginatedData = sortedData.slice(startIndex, endIndex);

    return {
      paginatedData,
      totalPages,
      startRow: startIndex + 1,
      endRow: endIndex
    };
  }, [sortedData, currentPage, rowsPerPage]);

  // Memoized comparison statistics
  const comparisonStats = useMemo(() => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return { matchedRows: 0, unmatchedRows: 0 };
    }

    let matchedRows = 0;
    let unmatchedRows = 0;
    const maxRows = Math.max(sourceResults.data.length, destinationResults.data.length);

    for (let i = 0; i < maxRows; i++) {
      const sourceRow = sourceResults.data[i];
      const destinationRow = destinationResults.data[i];

      if (!sourceRow || !destinationRow) {
        unmatchedRows++;
      } else {
        const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
        if (isMatch) {
          matchedRows++;
        } else {
          unmatchedRows++;
        }
      }
    }

    return { matchedRows, unmatchedRows };
  }, [comparison, sourceResults, destinationResults]);

  const getComparisonClass = (globalRowIndex: number) => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return '';
    }

    const sourceRow = sourceResults.data[globalRowIndex];
    const destinationRow = destinationResults.data[globalRowIndex];

    if (!sourceRow || !destinationRow) {
      return 'bg-red-50 border-red-200';
    }

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    return isMatch ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
  };

  // New function to get cell-level comparison class for unmatched values
  const getCellComparisonClass = (globalRowIndex: number, columnName: string, cellValue: any) => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return '';
    }

    const sourceRow = sourceResults.data[globalRowIndex];
    const destinationRow = destinationResults.data[globalRowIndex];

    // Only apply red text for unmatched rows
    if (!sourceRow || !destinationRow) {
      return 'text-red-600 font-semibold';
    }

    const isRowMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    if (isRowMatch) {
      return ''; // No special styling for matched rows
    }

    // For unmatched rows, check if this specific cell value is different
    const otherResults = type === 'source' ? destinationResults : sourceResults;
    const otherRow = otherResults.data[globalRowIndex];

    if (!otherRow) {
      return 'text-red-600 font-semibold';
    }

    const currentValue = cellValue;
    const otherValue = otherRow[columnName];

    // Compare the specific cell values
    const isCellMatch = JSON.stringify(currentValue) === JSON.stringify(otherValue);
    return isCellMatch ? '' : 'text-red-600 font-semibold';
  };



  // Sorting handler
  const handleSort = useCallback((columnKey: string) => {
    setSortConfig(prevConfig => {
      if (prevConfig?.key === columnKey) {
        // Toggle direction or clear sort
        if (prevConfig.direction === 'asc') {
          return { key: columnKey, direction: 'desc' };
        } else {
          return null; // Clear sort
        }
      } else {
        // New column sort
        return { key: columnKey, direction: 'asc' };
      }
    });
  }, []);

  // Auto-resize column to fit content
  const autoResizeColumn = useCallback((columnName: string) => {
    if (!results?.data || results.data.length === 0) return;

    // Create a temporary element to measure text width
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    if (!context) return;

    context.font = '14px system-ui, -apple-system, sans-serif'; // Match table font

    // Measure header width
    let maxWidth = context.measureText(columnName).width + 60; // Add padding for sort icon

    // Measure content width (sample first 100 rows for performance)
    const sampleSize = Math.min(100, results.data.length);
    for (let i = 0; i < sampleSize; i++) {
      const cellValue = String(results.data[i][columnName] || '');
      const textWidth = context.measureText(cellValue).width + 32; // Add padding
      maxWidth = Math.max(maxWidth, textWidth);
    }

    // Set reasonable bounds
    const finalWidth = Math.max(80, Math.min(400, maxWidth));

    setColumnWidths(prev => ({
      ...prev,
      [columnName]: finalWidth
    }));
  }, [results?.data]);

  // Column resizing handlers
  const handleMouseDown = useCallback((e: React.MouseEvent, columnName: string) => {
    e.preventDefault();
    setIsResizing(columnName);
    resizeStartX.current = e.clientX;
    resizeStartWidth.current = columnWidths[columnName] || 150;
  }, [columnWidths]);

  // Handle double-click for auto-resize
  const handleDoubleClick = useCallback((columnName: string) => {
    autoResizeColumn(columnName);
  }, [autoResizeColumn]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isResizing) return;

    const deltaX = e.clientX - resizeStartX.current;
    const newWidth = Math.max(80, resizeStartWidth.current + deltaX); // Minimum width of 80px

    setColumnWidths(prev => ({
      ...prev,
      [isResizing]: newWidth
    }));
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(null);
  }, []);

  // Add global mouse event listeners for column resizing
  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  // Reset to first page when results change
  React.useEffect(() => {
    setCurrentPage(1);
    setColumnWidths({}); // Reset column widths when results change
  }, [results]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-sm text-slate-600">Executing query...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 text-slate-400" />
          <p className="text-sm text-slate-600">No results to display</p>
          <p className="text-xs text-slate-400 mt-1">Execute a query to see results</p>
        </div>
      </div>
    );
  }

  const columns = results.data.length > 0 ? Object.keys(results.data[0]) : [];

  return (
    <div className="flex flex-col h-full space-y-4 min-h-0">

      {/* Pagination Controls - Top (Sticky) */}
      {paginationData.totalPages > 1 && (
        <div className="sticky top-[120px] z-10 bg-white border-b border-slate-200 pb-2">
          <div className="flex items-center justify-between bg-slate-50 rounded-lg p-3 border border-slate-200">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                First
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4" />
                Previous
              </Button>
            </div>

            <div className="text-sm font-medium text-slate-700">
              Page {currentPage} of {paginationData.totalPages}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))}
                disabled={currentPage === paginationData.totalPages}
              >
                Next
                <ChevronRight className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(paginationData.totalPages)}
                disabled={currentPage === paginationData.totalPages}
              >
                Last
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Results Table */}
      <div className="flex-1 border border-slate-200 rounded-lg overflow-hidden flex flex-col min-h-0">
        {/* Table Container with Fixed Horizontal Scrollbar */}
        <div className="relative flex-1 min-h-0">
          <div
            ref={tableRef}
            className="table-scroll-container flex-1 min-h-0"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 #f1f5f9',
              overflowX: 'scroll',
              overflowY: 'scroll', // Always show vertical scrollbar
              minHeight: '400px'
            }}
          >
            <Table className="min-w-full" style={{ minWidth: `${columns.length * 150}px` }}>
              <TableHeader className="sticky top-0 z-10 bg-slate-100 border-b-2 border-slate-300">
                <TableRow>
                  {columns.map((column, index) => {
                    const columnWidth = columnWidths[column] || (index === 0 ? 200 : 150);
                    const isSorted = sortConfig?.key === column;
                    const sortDirection = isSorted ? sortConfig.direction : null;

                    return (
                      <TableHead
                        key={column}
                        className="font-semibold text-slate-800 border-r border-slate-200 last:border-r-0 px-4 py-3 text-left whitespace-nowrap bg-slate-100 relative group cursor-pointer hover:bg-slate-200 transition-colors"
                        style={{
                          width: columnWidth,
                          minWidth: columnWidth,
                          maxWidth: columnWidth
                        }}
                        onClick={() => handleSort(column)}
                      >
                        <div className="flex items-center justify-between pr-6" title={`${column} - Click to sort, double-click resize handle to auto-fit`}>
                          <span className="truncate">{column}</span>
                          <div className="flex items-center ml-2">
                            {/* Sort Icon */}
                            {isSorted ? (
                              sortDirection === 'asc' ? (
                                <ArrowUp className="w-4 h-4 text-blue-600" />
                              ) : (
                                <ArrowDown className="w-4 h-4 text-blue-600" />
                              )
                            ) : (
                              <ArrowUpDown className="w-4 h-4 text-slate-400 group-hover:text-slate-600" />
                            )}
                          </div>
                        </div>
                        {/* Column Resize Handle */}
                        <div
                          className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize bg-transparent hover:bg-blue-400 group-hover:bg-blue-300 transition-colors"
                          onMouseDown={(e) => {
                            e.stopPropagation(); // Prevent sort when resizing
                            handleMouseDown(e, column);
                          }}
                          onDoubleClick={(e) => {
                            e.stopPropagation(); // Prevent sort when auto-resizing
                            handleDoubleClick(column);
                          }}
                          style={{
                            right: '-2px',
                            width: '4px',
                            zIndex: 20
                          }}
                          title="Drag to resize, double-click to auto-fit"
                        />
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginationData.paginatedData.map((row, index) => {
                  const globalIndex = (currentPage - 1) * rowsPerPage + index;
                  return (
                    <TableRow
                      key={globalIndex}
                      className={`table-row-optimized hover:bg-slate-50 transition-colors border-b border-slate-100 ${getComparisonClass(globalIndex)}`}
                    >
                      {columns.map((column, colIndex) => {
                        const cellValue = row[column];
                        const displayValue = cellValue !== null && cellValue !== undefined ? String(cellValue) : '';
                        const columnWidth = columnWidths[column] || (colIndex === 0 ? 200 : 150);

                        const cellComparisonClass = getCellComparisonClass(globalIndex, column, cellValue);

                        return (
                          <TableCell
                            key={column}
                            className={`border-r border-slate-100 last:border-r-0 text-sm px-4 py-2 whitespace-nowrap overflow-hidden text-ellipsis ${cellComparisonClass}`}
                            style={{
                              width: columnWidth,
                              minWidth: columnWidth,
                              maxWidth: columnWidth
                            }}
                            title={displayValue} // Show full value on hover
                          >
                            <div className="truncate">
                              {displayValue}
                            </div>
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>
      </div>

      {/* Pagination Controls - Bottom */}
      {paginationData.totalPages > 1 && (
        <div className="flex items-center justify-between bg-slate-50 rounded-lg p-3 border border-slate-200">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
            >
              First
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="w-4 h-4" />
              Previous
            </Button>
          </div>

          <div className="text-sm font-medium text-slate-700">
            Page {currentPage} of {paginationData.totalPages}
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(prev => Math.min(paginationData.totalPages, prev + 1))}
              disabled={currentPage === paginationData.totalPages}
            >
              Next
              <ChevronRight className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(paginationData.totalPages)}
              disabled={currentPage === paginationData.totalPages}
            >
              Last
            </Button>
          </div>
        </div>
      )}

      {/* Comparison Legend */}
      {comparison === 'completed' && (
        <div className="bg-slate-50 rounded-lg p-4 border border-slate-200">
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-slate-700">Matched Rows</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="text-slate-700">Mismatched Rows</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsTable;
