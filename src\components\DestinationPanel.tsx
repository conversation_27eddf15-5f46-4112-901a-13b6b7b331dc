import React, { useState, useEffect, useMemo } from 'react';
import { Database, Server, FileText, BarChart3, Info, Wifi, WifiOff, Plus, Minus, Trash2, ChevronDown, RotateCcw } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useQuery } from '../context/QueryContext';
import { useToast } from '@/hooks/use-toast';
import ResultsTable from './ResultsTable';
import { databaseService } from '../services/databaseService';

const DestinationPanel = () => {
  const {
    destinationServer,
    setDestinationServer,
    destinationDatabase,
    setDestinationDatabase,
    destinationQuery,
    setDestinationQuery,
    destinationResults,
    setDestinationResults,
    isDestinationLoading,
    comparison,
    setComparison,
    isDestinationConnected,
    setIsDestinationConnected,
    isDestinationAccordionCollapsed,
    setIsDestinationAccordionCollapsed,
    sourceResults,
  } = useQuery();

  const [filters, setFilters] = useState<Record<string, string>>({});
  const [isQueryResultsCollapsed, setIsQueryResultsCollapsed] = useState(false);
  const [isFiltersCollapsed, setIsFiltersCollapsed] = useState(true);
  const [availableDatabases, setAvailableDatabases] = useState<string[]>([]);
  const [serverInfo, setServerInfo] = useState<{ name: string; alias: string; user: string } | null>(null);
  const [isLoadingServerInfo, setIsLoadingServerInfo] = useState(true);
  const { toast } = useToast();

  // Load server info and databases on component mount
  useEffect(() => {
    const loadServerInfo = async () => {
      try {
        setIsLoadingServerInfo(true);
        console.log('Loading destination server info...');
        const info = await databaseService.getServerInfo();
        console.log('Destination server info loaded:', info);
        setServerInfo(info);
        setDestinationServer(info.alias); // Set the server alias as default
      } catch (error) {
        console.error('Failed to load destination server info:', error);
        toast({
          title: "Server Loading Failed",
          description: "Could not retrieve server information.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingServerInfo(false);
      }
    };

    loadServerInfo();
  }, [setDestinationServer, toast]);

  // Load databases when server is selected
  useEffect(() => {
    const loadDatabases = async () => {
      if (destinationServer && serverInfo) {
        try {
          const databases = await databaseService.getDatabases();
          setAvailableDatabases(databases);
        } catch (error) {
          console.error('Failed to load databases:', error);
          toast({
            title: "Database Loading Failed",
            description: "Could not retrieve database list from server.",
            variant: "destructive",
          });
        }
      }
    };

    loadDatabases();
  }, [destinationServer, serverInfo, toast]);

  const handleConnectionToggle = async (checked: boolean) => {
    if (checked) {
      if (!destinationServer || !destinationDatabase) {
        toast({
          title: "Connection Failed",
          description: "Please select server and database first.",
          variant: "destructive",
        });
        return;
      }

      try {
        const connectionResult = await databaseService.testConnection();
        if (connectionResult.success) {
          setIsDestinationConnected(true);
          toast({
            title: "Connection Successful",
            description: `Connected to ${destinationServer}/${destinationDatabase}`,
          });
        } else {
          throw new Error(connectionResult.message || 'Connection failed');
        }
      } catch (error) {
        toast({
          title: "Connection Failed",
          description: error instanceof Error ? error.message : 'Unknown error occurred',
          variant: "destructive",
        });
      }
    } else {
      setIsDestinationConnected(false);
      toast({
        title: "Disconnected",
        description: "Destination database connection closed.",
      });
    }
  };

  const handleFilterChange = (column: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [column]: value
    }));
  };

  const clearDestinationData = () => {
    setDestinationQuery('');
    setDestinationResults(null);
    setFilters({});
    setComparison('none');
    setIsDestinationAccordionCollapsed(false); // Show the section
    toast({
      title: "Destination Data Cleared",
      description: "Destination query and results have been cleared.",
    });
  };

  // Clear all filters
  const clearAllFilters = () => {
    setFilters({});
    toast({
      title: "Filters Cleared",
      description: "All filters have been reset.",
    });
  };

  // Check if any filters are applied
  const hasActiveFilters = Object.values(filters).some(value => value && value.trim() !== '');

  // Hide filters when query execution starts
  useEffect(() => {
    if (isDestinationLoading) {
      setIsFiltersCollapsed(true);
    }
  }, [isDestinationLoading]);

  // Get unique values for each column for dropdown filters
  const getColumnUniqueValues = (columnName: string) => {
    if (!destinationResults?.data) return [];

    const uniqueValues = new Set();
    destinationResults.data.forEach(row => {
      const value = row[columnName];
      if (value !== null && value !== undefined) {
        uniqueValues.add(String(value));
      }
    });

    return Array.from(uniqueValues).sort().slice(0, 100); // Limit to 100 values for performance
  };

  const getFilteredResults = () => {
    if (!destinationResults?.data) return destinationResults;
    
    const filteredData = destinationResults.data.filter(row => {
      return Object.entries(filters).every(([column, filterValue]) => {
        if (!filterValue) return true;
        return String(row[column]).toLowerCase().includes(filterValue.toLowerCase());
      });
    });

    return {
      ...destinationResults,
      data: filteredData,
      totalRows: filteredData.length
    };
  };

  const columns = destinationResults?.data.length > 0 ? Object.keys(destinationResults.data[0]) : [];

  // Memoized comparison statistics
  const comparisonStats = useMemo(() => {
    if (comparison !== 'completed' || !sourceResults || !destinationResults) {
      return { matchedRows: 0, unmatchedRows: 0 };
    }

    let matchedRows = 0;
    let unmatchedRows = 0;
    const maxRows = Math.max(sourceResults.data.length, destinationResults.data.length);

    for (let i = 0; i < maxRows; i++) {
      const sourceRow = sourceResults.data[i];
      const destinationRow = destinationResults.data[i];

      if (!sourceRow || !destinationRow) {
        unmatchedRows++;
      } else {
        const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
        if (isMatch) {
          matchedRows++;
        } else {
          unmatchedRows++;
        }
      }
    }

    return { matchedRows, unmatchedRows };
  }, [comparison, sourceResults, destinationResults]);

  return (
    <div className="h-full flex flex-col bg-gradient-to-b from-green-50 to-emerald-50 shadow-lg shadow-black/50 rounded-lg overflow-hidden">
      <Collapsible open={!isDestinationAccordionCollapsed} onOpenChange={(open) => setIsDestinationAccordionCollapsed(!open)}>
        <div className="p-3 sm:p-4 lg:p-5 border-b-2 border-green-200 bg-gradient-to-r from-green-100 to-emerald-100 rounded-t-lg">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              {/* Clear Icon */}
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      onClick={clearDestinationData}
                      variant="ghost"
                      size="sm"
                      className="flex items-center gap-2 hover:bg-red-50 text-red-600 hover:text-red-700 transition-all duration-300 h-auto p-2"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span className="text-xs font-medium">Clear</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Clear destination query and results</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <div className="p-2 bg-green-600 rounded-full">
                <Database className="w-3 h-3 sm:w-4 sm:h-4 lg:w-6 lg:h-6 text-white" />
              </div>
              <h2 className="text-base sm:text-lg lg:text-xl font-bold text-green-900">
                Destination Activities
              </h2>
            </div>

            <div className="flex items-center gap-3">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-green-800">
                        {isDestinationConnected ? 'Disconnect' : 'Connect'}
                      </span>
                      <Switch
                        checked={isDestinationConnected}
                        onCheckedChange={handleConnectionToggle}
                        className="data-[state=checked]:bg-green-600"
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>{isDestinationConnected ? 'Disconnect from server' : 'Connect to server'}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              {/* Connection Status Indicator moved here */}
              {isDestinationConnected ? (
                <Wifi className="w-4 h-4 text-green-600" />
              ) : (
                <WifiOff className="w-4 h-4 text-red-600" />
              )}

              {/* Show/Hide Icon moved to the end */}
              <CollapsibleTrigger asChild>
                <button className="p-1 hover:bg-green-200 rounded-full transition-colors">
                  {isDestinationAccordionCollapsed ? (
                    <Plus className="w-4 h-4 text-green-600" />
                  ) : (
                    <Minus className="w-4 h-4 text-green-600" />
                  )}
                </button>
              </CollapsibleTrigger>
            </div>
          </div>


          
          <CollapsibleContent>
            <div className="space-y-6">
              {/* Desktop: Grid layout, Mobile/Tablet: Stacked layout */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Label htmlFor="destination-server" className="text-sm font-semibold text-green-800 flex items-center gap-2">
                          <Server className="w-4 h-4" />
                          Select Destination Server
                          <Info className="w-3 h-3 text-green-600 cursor-help" />
                        </Label>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Choose the server where your destination data is located</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Select value={destinationServer} onValueChange={setDestinationServer} disabled={isLoadingServerInfo}>
                    <SelectTrigger className="border-2 border-green-300 focus:border-green-500 bg-white shadow-md">
                      <SelectValue placeholder={isLoadingServerInfo ? "Loading servers..." : "Choose server..."} />
                    </SelectTrigger>
                    <SelectContent>
                      {isLoadingServerInfo ? (
                        <SelectItem value="loading" disabled>
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 animate-spin rounded-full border-2 border-green-600 border-t-transparent"></div>
                            Loading...
                          </div>
                        </SelectItem>
                      ) : serverInfo ? (
                        <SelectItem key={serverInfo.alias} value={serverInfo.alias}>
                          <div className="flex items-center gap-2">
                            <Server className="w-4 h-4 text-green-600" />
                            {serverInfo.alias} ({serverInfo.name})
                          </div>
                        </SelectItem>
                      ) : (
                        <SelectItem value="no-server" disabled>
                          <div className="flex items-center gap-2 text-red-600">
                            <Server className="w-4 h-4" />
                            No server available
                          </div>
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Label htmlFor="destination-database" className="text-sm font-semibold text-green-800 flex items-center gap-2">
                          <Database className="w-4 h-4" />
                          Select Database
                          <Info className="w-3 h-3 text-green-600 cursor-help" />
                        </Label>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Select the specific database to query from</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                  <Select value={destinationDatabase} onValueChange={setDestinationDatabase} disabled={!destinationServer}>
                    <SelectTrigger className="border-2 border-green-300 focus:border-green-500 bg-white shadow-md">
                      <SelectValue placeholder="Choose database..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableDatabases.map((database) => (
                        <SelectItem key={database} value={database}>
                          <div className="flex items-center gap-2">
                            <Database className="w-4 h-4 text-green-600" />
                            {database}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Label htmlFor="destination-query" className="text-sm font-semibold text-green-800 flex items-center gap-2">
                        <FileText className="w-4 h-4" />
                        SQL Query
                        <Info className="w-3 h-3 text-green-600 cursor-help" />
                      </Label>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Enter your SQL query to execute on the destination database</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                <Textarea
                  id="destination-query"
                  value={destinationQuery}
                  onChange={(e) => setDestinationQuery(e.target.value)}
                  placeholder="SELECT * FROM table_name WHERE condition..."
                  className="mt-2 h-32 sm:h-40 font-mono text-sm resize-none border-2 border-green-300 focus:border-green-500 bg-white shadow-md"
                  disabled={!isDestinationConnected}
                />
              </div>
            </div>
          </CollapsibleContent>
        </div>
      </Collapsible>
      
      <div className="flex-1 p-3 sm:p-4 lg:p-5 overflow-hidden bg-white rounded-b-lg min-h-0 flex flex-col">
        <Collapsible open={!isQueryResultsCollapsed} onOpenChange={(open) => setIsQueryResultsCollapsed(!open)} className="flex-1 flex flex-col min-h-0">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-4 flex-wrap">
              <div className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5 text-green-600" />
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <h3 className="text-base sm:text-lg font-semibold text-green-900 flex items-center gap-2">
                        Query Results
                        <Info className="w-3 h-3 text-green-600 cursor-help" />
                      </h3>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Results from executing your destination query</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              {/* Statistics */}
              {destinationResults && (
                <div className="flex items-center gap-4 flex-wrap">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium text-slate-700">
                      Total Rows: {destinationResults.data.length.toLocaleString()}
                    </span>
                  </div>

                  {comparison === 'completed' && (
                    <>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm font-medium text-green-700">
                          Matched: {comparisonStats.matchedRows.toLocaleString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span className="text-sm font-medium text-red-700">
                          Unmatched: {comparisonStats.unmatchedRows.toLocaleString()}
                        </span>
                      </div>
                    </>
                  )}
                </div>
              )}
            </div>
            <CollapsibleTrigger asChild>
              <button className="p-1 hover:bg-green-200 rounded-full transition-colors">
                {isQueryResultsCollapsed ? (
                  <Plus className="w-4 h-4 text-green-600" />
                ) : (
                  <Minus className="w-4 h-4 text-green-600" />
                )}
              </button>
            </CollapsibleTrigger>
          </div>

          <CollapsibleContent className="flex-1 flex flex-col min-h-0">
            {/* Collapsible Filters */}
            {destinationResults && columns.length > 0 && (
              <Collapsible open={!isFiltersCollapsed} onOpenChange={(open) => setIsFiltersCollapsed(!open)}>
                <div className="mb-4">
                  <div className="flex items-center justify-between p-3 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                    <div className="flex items-center gap-2">
                      <h4 className="text-sm font-semibold text-green-800">Filter Results</h4>
                      <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">
                        {Object.keys(filters).filter(key => filters[key]).length} active
                      </span>
                      {/* Filter Reset Icon - only show if filters are active */}
                      {hasActiveFilters && (
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                onClick={clearAllFilters}
                                variant="ghost"
                                size="sm"
                                className="flex items-center gap-1 hover:bg-red-50 text-red-600 hover:text-red-700 transition-all duration-300 h-auto p-1 ml-2"
                              >
                                <RotateCcw className="w-3 h-3" />
                                <span className="text-xs font-medium">Reset</span>
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Clear all filters</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      )}
                    </div>
                    <CollapsibleTrigger asChild>
                      <button className="p-1 hover:bg-green-200 rounded-full transition-colors">
                        {isFiltersCollapsed ? (
                          <Plus className="w-4 h-4 text-green-600" />
                        ) : (
                          <Minus className="w-4 h-4 text-green-600" />
                        )}
                      </button>
                    </CollapsibleTrigger>
                  </div>
                  <CollapsibleContent>
                    <div className="mt-2 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg border border-green-200">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                        {columns.map((column) => {
                          const uniqueValues = getColumnUniqueValues(column);
                          return (
                            <div key={column} className="space-y-1">
                              <Label className="text-xs font-medium text-green-700">{column}</Label>
                              <Select
                                value={filters[column] || ''}
                                onValueChange={(value) => handleFilterChange(column, value === 'all' ? '' : value)}
                              >
                                <SelectTrigger className="w-full text-sm border border-green-300 focus:border-green-500 bg-white">
                                  <SelectValue placeholder={`Filter by ${column}...`} />
                                </SelectTrigger>
                                <SelectContent className="max-h-60">
                                  <SelectItem value="all">
                                    <span className="text-slate-500">All values</span>
                                  </SelectItem>
                                  {uniqueValues.map((value) => (
                                    <SelectItem key={value} value={value}>
                                      <div className="flex items-center gap-2">
                                        <ChevronDown className="w-3 h-3 text-green-500" />
                                        <span className="truncate max-w-40">{value}</span>
                                      </div>
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  </CollapsibleContent>
                </div>
              </Collapsible>
            )}

            <div className="flex-1 min-h-0">
              <ResultsTable
                results={getFilteredResults()}
                loading={isDestinationLoading}
                comparison={comparison}
                type="destination"
              />
            </div>
          </CollapsibleContent>
        </Collapsible>
      </div>
    </div>
  );
};

export default DestinationPanel;
