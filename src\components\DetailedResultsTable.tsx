import React, { useState, useMemo, useCallback, useRef, useEffect } from 'react';
import { Loader2, FileText, ArrowUpDown, ArrowUp, ArrowDown } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useQuery } from '../context/QueryContext';

interface DetailedResultsTableProps {
  results: {
    data: Record<string, any>[];
    totalRows: number;
    displayedRows?: number;
    isLimited?: boolean;
  } | null;
  loading: boolean;
  type: 'source' | 'destination';
  currentRowIndex: number;
  onScrollToRow?: (rowIndex: number) => void;
  onRowClick?: (rowIndex: number, rowData: Record<string, any>) => void;
  displayFilter?: 'all' | 'matched' | 'unmatched';
  visibleRowIndices?: number[]; // New prop to specify which rows to show
}

const DetailedResultsTable = ({ results, loading, type, currentRowIndex, onScrollToRow, onRowClick, displayFilter = 'all', visibleRowIndices }: DetailedResultsTableProps) => {
  const { sourceResults, destinationResults } = useQuery();

  // Column width state
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [isResizing, setIsResizing] = useState<string | null>(null);
  const tableRef = useRef<HTMLDivElement>(null);
  const scrollbarRef = useRef<HTMLDivElement>(null);
  const resizeStartX = useRef<number>(0);
  const resizeStartWidth = useRef<number>(0);

  // Drag scrolling state
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, scrollLeft: 0 });
  const [clickedRowIndex, setClickedRowIndex] = useState<number | null>(null);

  // Sorting state
  const [sortColumn, setSortColumn] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  // Initialize sorting with first column
  React.useEffect(() => {
    if (results?.data && results.data.length > 0 && !sortColumn) {
      const firstColumn = Object.keys(results.data[0])[0];
      setSortColumn(firstColumn);
      setSortDirection('asc');
    }
  }, [results, sortColumn]);

  // Memoized sorted data with functional sorting
  const sortedData = useMemo(() => {
    if (!results?.data) return [];

    const dataToSort = [...results.data];

    if (!sortColumn) return dataToSort;

    // Create array of data with original indices for status sorting
    const dataWithIndices = dataToSort.map((item, originalIndex) => ({
      data: item,
      originalIndex
    }));

    const sorted = dataWithIndices.sort((a, b) => {
      const aVal = a.data[sortColumn];
      const bVal = b.data[sortColumn];

      // Handle null/undefined values
      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return sortDirection === 'asc' ? -1 : 1;
      if (bVal == null) return sortDirection === 'asc' ? 1 : -1;

      // Try numeric comparison first
      const aNum = Number(aVal);
      const bNum = Number(bVal);

      if (!isNaN(aNum) && !isNaN(bNum)) {
        return sortDirection === 'asc' ? aNum - bNum : bNum - aNum;
      }

      // Fall back to string comparison
      const aStr = String(aVal).toLowerCase();
      const bStr = String(bVal).toLowerCase();

      if (aStr < bStr) return sortDirection === 'asc' ? -1 : 1;
      if (aStr > bStr) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return sorted.map(item => item.data);
  }, [results, sortColumn, sortDirection]);

  // Helper function to determine if a row is matched
  const isRowMatched = useCallback((rowIndex: number) => {
    if (!sourceResults || !destinationResults) return false;

    const sourceRow = sourceResults.data[rowIndex];
    const destinationRow = destinationResults.data[rowIndex];

    if (!sourceRow || !destinationRow) return false;

    return JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
  }, [sourceResults, destinationResults]);

  // Show all data without pagination with original indices and apply filtering
  const allData = useMemo(() => {
    if (!results?.data) return [];

    // If visibleRowIndices is provided, use it to filter the data
    let dataToShow = results.data;
    if (visibleRowIndices && visibleRowIndices.length > 0) {
      dataToShow = visibleRowIndices.map(index => results.data[index]).filter(Boolean);
    }

    // Apply sorting to the filtered data
    let sortedFilteredData = dataToShow;
    if (sortColumn && sortDirection) {
      sortedFilteredData = [...dataToShow].sort((a, b) => {
        const aVal = a[sortColumn];
        const bVal = b[sortColumn];

        if (aVal === null || aVal === undefined) return 1;
        if (bVal === null || bVal === undefined) return -1;

        let comparison = 0;
        if (typeof aVal === 'string' && typeof bVal === 'string') {
          comparison = aVal.localeCompare(bVal);
        } else {
          comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        }

        return sortDirection === 'desc' ? -comparison : comparison;
      });
    }

    // Map to include original indices
    let dataWithIndices = sortedFilteredData.map((row, index) => {
      // Find the original index in the full dataset
      const originalIndex = results.data.findIndex(originalRow =>
        JSON.stringify(originalRow) === JSON.stringify(row)
      );
      return {
        data: row,
        originalIndex: originalIndex !== -1 ? originalIndex : index
      };
    });

    // If a row was clicked during sorting, move it to the front
    if (clickedRowIndex !== null && sortColumn) {
      const clickedRowData = dataWithIndices.find(item => item.originalIndex === clickedRowIndex);
      if (clickedRowData) {
        const filteredData = dataWithIndices.filter(item => item.originalIndex !== clickedRowIndex);
        dataWithIndices = [clickedRowData, ...filteredData];
      }
    }

    return dataWithIndices;
  }, [results, visibleRowIndices, sortColumn, sortDirection, clickedRowIndex]);

  // Reset column widths when results change
  React.useEffect(() => {
    setColumnWidths({}); // Reset column widths when results change
  }, [results]);

  // Sync scrolling between table and fixed scrollbar
  React.useEffect(() => {
    const tableElement = tableRef.current;
    const scrollbarElement = scrollbarRef.current;

    if (!tableElement || !scrollbarElement) return;

    const handleTableScroll = () => {
      scrollbarElement.scrollLeft = tableElement.scrollLeft;
    };

    tableElement.addEventListener('scroll', handleTableScroll);

    return () => {
      tableElement.removeEventListener('scroll', handleTableScroll);
    };
  }, []);

  // Sorting handler
  const handleSort = useCallback((column: string) => {
    if (sortColumn === column) {
      setSortDirection(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortColumn(column);
      setSortDirection('asc');
    }
  }, [sortColumn]);

  // Scroll to row functionality
  const scrollToRow = useCallback((rowIndex: number) => {
    if (!tableRef.current) return;

    const tableBody = tableRef.current.querySelector('tbody');
    if (!tableBody) return;

    const rows = tableBody.querySelectorAll('tr');
    const targetRow = rows[rowIndex];

    if (targetRow) {
      targetRow.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  }, []);

  // Effect to scroll to current row when it changes
  React.useEffect(() => {
    if (currentRowIndex >= 0) {
      scrollToRow(currentRowIndex);
    }
  }, [currentRowIndex, scrollToRow]);

  // Column resizing handlers
  const handleColumnResizeMouseDown = useCallback((e: React.MouseEvent, column: string) => {
    e.preventDefault();
    setIsResizing(column);
    resizeStartX.current = e.clientX;
    resizeStartWidth.current = columnWidths[column] || 150;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      const diff = e.clientX - resizeStartX.current;
      const newWidth = Math.max(50, resizeStartWidth.current + diff);
      setColumnWidths(prev => ({ ...prev, [column]: newWidth }));
    };

    const handleMouseUp = () => {
      setIsResizing(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [isResizing, columnWidths]);

  const handleDoubleClick = useCallback((column: string) => {
    // Auto-fit column width based on content
    if (!results?.data || !tableRef.current) return;

    // Calculate optimal width based on content
    const maxLength = Math.max(
      column.length, // Header length
      ...results.data.map(row => String(row[column] || '').length)
    );

    // Set width based on character count (approximate 8px per character + padding)
    const optimalWidth = Math.min(Math.max(maxLength * 8 + 32, 100), 400);
    setColumnWidths(prev => ({ ...prev, [column]: optimalWidth }));
  }, [results]);

  // Drag scrolling handlers
  const handleTableDragStart = useCallback((e: React.MouseEvent) => {
    if (isResizing) return; // Don't start drag if resizing

    // Only start drag if clicking on empty space (not on table rows or headers)
    const target = e.target as HTMLElement;
    if (target.closest('tr') || target.closest('th')) return;

    const tableElement = tableRef.current;
    if (!tableElement) return;

    setIsDragging(true);
    setDragStart({
      x: e.pageX,
      scrollLeft: tableElement.scrollLeft
    });

    // Prevent text selection during drag
    e.preventDefault();
  }, [isResizing]);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !tableRef.current) return;

    const x = e.pageX;
    const walk = (x - dragStart.x) * 2; // Multiply by 2 for faster scrolling
    tableRef.current.scrollLeft = dragStart.scrollLeft - walk;
  }, [isDragging, dragStart]);

  const handleDragMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  // Add event listeners for drag scrolling
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleDragMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleDragMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleDragMouseUp]);

  // Function to get row highlighting for first row only
  const getRowHighlighting = (rowIndex: number) => {
    if (rowIndex !== currentRowIndex) return ''; // Only highlight current row

    if (!sourceResults?.data || !destinationResults?.data) return '';

    const sourceRow = sourceResults.data[rowIndex];
    const destinationRow = destinationResults.data[rowIndex];

    if (!sourceRow || !destinationRow) return 'bg-red-100'; // Red if missing

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    return isMatch ? 'bg-green-100' : 'bg-red-100'; // Green if match, red if different
  };

  // Function to get match status for a row
  const getRowMatchStatus = (rowIndex: number) => {
    if (!sourceResults?.data || !destinationResults?.data) return 'missing';

    const sourceRow = sourceResults.data[rowIndex];
    const destinationRow = destinationResults.data[rowIndex];

    if (!sourceRow || !destinationRow) return 'missing';

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    return isMatch ? 'match' : 'mismatch';
  };

  // Function to check if a specific cell value differs
  const isCellDifferent = (rowIndex: number, column: string) => {
    if (!sourceResults?.data || !destinationResults?.data) return false;

    const sourceRow = sourceResults.data[rowIndex];
    const destinationRow = destinationResults.data[rowIndex];

    if (!sourceRow || !destinationRow) return false;

    return sourceRow[column] !== destinationRow[column];
  };



  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-sm text-slate-600">Executing query...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 text-slate-400" />
          <p className="text-sm text-slate-600">No results to display</p>
          <p className="text-xs text-slate-400 mt-1">Execute a query to see results</p>
        </div>
      </div>
    );
  }

  const columns = results.data.length > 0 ? Object.keys(results.data[0]) : [];
  const allColumns = ['Row Status', ...columns]; // Add Row Status column as first column

  return (
    <div className="flex flex-col h-full space-y-4 min-h-0">
      {/* Results Table */}
      <div className="flex-1 border border-slate-200 rounded-lg flex flex-col min-h-0 relative">
        {/* Table Container */}
        <div className="flex-1 min-h-0 relative">
          <div
            ref={tableRef}
            className="table-scroll-container h-full w-full cursor-grab active:cursor-grabbing"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 #f1f5f9',
              overflowY: 'auto',
              overflowX: 'auto', // Show horizontal scrollbar
              minHeight: '300px',
              maxHeight: '100%'
            }}
            onMouseDown={handleTableDragStart}
          >
            <Table className="w-full" style={{ minWidth: `${Math.max(allColumns.length * 150, 800)}px` }}>
              <TableHeader className="detailed-table-header border-b-2 border-slate-300 shadow-sm">
                <TableRow>
                  {allColumns.map((column, index) => {
                    const isStatusColumn = column === 'Row Status';
                    const columnWidth = isStatusColumn ? 100 : (columnWidths[column] || (index === 1 ? 200 : 150));
                    const actualColumn = isStatusColumn ? '' : column;
                    const isCurrentSort = sortColumn === actualColumn;

                    return (
                      <TableHead
                        key={column}
                        className={`font-semibold text-slate-800 border-r border-slate-200 last:border-r-0 px-4 py-3 text-left whitespace-nowrap bg-white relative group sticky-header ${!isStatusColumn ? 'cursor-pointer hover:bg-slate-50' : ''}`}
                        style={{
                          width: columnWidth,
                          minWidth: columnWidth,
                          maxWidth: columnWidth,
                          position: 'sticky',
                          top: 0,
                          zIndex: 19,
                          backgroundColor: 'white'
                        }}
                        onClick={() => !isStatusColumn && handleSort(actualColumn)}
                      >
                        <div className="flex items-center justify-between pr-6">
                          <span className="truncate">{column}</span>
                          {!isStatusColumn && (
                            <div className="flex items-center ml-2">
                              {isCurrentSort ? (
                                sortDirection === 'asc' ? (
                                  <ArrowUp className="w-4 h-4 text-blue-600" />
                                ) : (
                                  <ArrowDown className="w-4 h-4 text-blue-600" />
                                )
                              ) : (
                                <ArrowUpDown className="w-4 h-4 text-slate-400 group-hover:text-slate-600" />
                              )}
                            </div>
                          )}
                        </div>
                        {!isStatusColumn && (
                          /* Column Resize Handle */
                          <div
                            className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize bg-transparent hover:bg-blue-400 group-hover:bg-blue-300 transition-colors"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              handleColumnResizeMouseDown(e, column);
                            }}
                            onDoubleClick={(e) => {
                              e.stopPropagation();
                              handleDoubleClick(column);
                            }}
                            style={{
                              right: '-2px',
                              width: '4px',
                              zIndex: 20
                            }}
                            title="Drag to resize, double-click to auto-fit"
                          />
                        )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {allData.map((rowItem, displayIndex) => {
                  const row = rowItem.data;
                  const originalIndex = rowItem.originalIndex;
                  const rowHighlighting = getRowHighlighting(originalIndex);
                  const matchStatus = getRowMatchStatus(originalIndex);

                  return (
                    <TableRow
                      key={displayIndex}
                      className={`table-row-optimized transition-all duration-300 border-b border-slate-100 bg-white ${rowHighlighting} ${originalIndex !== currentRowIndex ? 'hover:bg-slate-50 cursor-pointer' : ''} ${clickedRowIndex === originalIndex && sortColumn ? 'transform scale-105 shadow-lg bg-blue-100' : ''}`}
                      onClick={(e) => {
                        // If sorting is active, set clicked row for movement
                        if (sortColumn) {
                          setClickedRowIndex(originalIndex);
                          // Clear the clicked row after a short delay to allow for visual feedback
                          setTimeout(() => setClickedRowIndex(null), 500);
                        }
                        // Call the original row click handler
                        if (onRowClick) {
                          onRowClick(originalIndex, row);
                        }
                      }}
                    >
                      {/* Row Status Indicator Column */}
                      <TableCell
                        className="border-r border-slate-100 text-sm px-4 py-2 whitespace-nowrap text-center"
                        style={{
                          width: 100,
                          minWidth: 100,
                          maxWidth: 100
                        }}
                      >
                        <div className="flex justify-center">
                          {matchStatus === 'match' && (
                            <div className="w-3 h-3 bg-green-500 rounded-full" title="Row matches"></div>
                          )}
                          {matchStatus === 'mismatch' && (
                            <div className="w-3 h-3 bg-red-500 rounded-full" title="Row differs"></div>
                          )}
                          {matchStatus === 'missing' && (
                            <div className="w-3 h-3 bg-gray-400 rounded-full" title="Row missing"></div>
                          )}
                        </div>
                      </TableCell>

                      {/* Data Columns */}
                      {columns.map((column, colIndex) => {
                        const cellValue = row[column];
                        const displayValue = cellValue !== null && cellValue !== undefined ? String(cellValue) : '';
                        const columnWidth = columnWidths[column] || (colIndex === 0 ? 200 : 150);
                        const isDifferent = isCellDifferent(originalIndex, column);
                        const isCurrentRowMismatch = originalIndex === currentRowIndex && getRowMatchStatus(originalIndex) === 'mismatch';

                        return (
                          <TableCell
                            key={column}
                            className="border-r border-slate-100 last:border-r-0 text-sm px-4 py-2 whitespace-nowrap overflow-hidden text-ellipsis"
                            style={{
                              width: columnWidth,
                              minWidth: columnWidth,
                              maxWidth: columnWidth
                            }}
                            title={displayValue} // Show full value on hover
                          >
                            <div
                              className={`truncate ${isDifferent && isCurrentRowMismatch ? 'text-red-600 font-medium' : ''}`}
                            >
                              {displayValue}
                            </div>
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Fixed Horizontal Scrollbar - Always Visible */}
        <div
          ref={scrollbarRef}
          className="w-full bg-white border-t border-slate-200 z-20"
          style={{
            height: '17px',
            overflowX: 'scroll', // Always show horizontal scrollbar
            overflowY: 'hidden',
            scrollbarWidth: 'thin',
            scrollbarColor: '#cbd5e1 #f1f5f9'
          }}
          onScroll={(e) => {
            if (tableRef.current) {
              tableRef.current.scrollLeft = e.currentTarget.scrollLeft;
            }
          }}
        >
          <div style={{ width: `${Math.max(allColumns.length * 150, 800)}px`, height: '1px' }}></div>
        </div>
      </div>
    </div>
  );
};

export default DetailedResultsTable;
