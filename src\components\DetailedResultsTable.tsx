import React, { useState, useMemo, useCallback, useRef } from 'react';
import { Loader2, <PERSON>Tex<PERSON>, ArrowUpDown, ArrowU<PERSON>, ArrowDown } from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useQuery } from '../context/QueryContext';

interface DetailedResultsTableProps {
  results: {
    data: Record<string, any>[];
    totalRows: number;
    displayedRows?: number;
    isLimited?: boolean;
  } | null;
  loading: boolean;
  type: 'source' | 'destination';
  currentRowIndex: number;
}

const DetailedResultsTable = ({ results, loading, type, currentRowIndex }: DetailedResultsTableProps) => {
  const { sourceResults, destinationResults } = useQuery();

  // Column width state
  const [columnWidths, setColumnWidths] = useState<Record<string, number>>({});
  const [isResizing, setIsResizing] = useState<string | null>(null);
  const tableRef = useRef<HTMLDivElement>(null);
  const scrollbarRef = useRef<HTMLDivElement>(null);
  const resizeStartX = useRef<number>(0);
  const resizeStartWidth = useRef<number>(0);

  // No sorting state needed - always sort by primary key in ascending order

  // Memoized sorted data with default ascending sort by primary key (first column)
  const sortedData = useMemo(() => {
    if (!results?.data) return [];

    const dataToSort = [...results.data];

    // Always sort by first column (primary key) in ascending order for detailed view
    const primaryKey = dataToSort.length > 0 ? Object.keys(dataToSort[0])[0] : '';

    if (!primaryKey) return dataToSort;

    return dataToSort.sort((a, b) => {
      const aVal = a[primaryKey];
      const bVal = b[primaryKey];

      // Handle null/undefined values
      if (aVal == null && bVal == null) return 0;
      if (aVal == null) return -1;
      if (bVal == null) return 1;

      // Try numeric comparison first
      const aNum = Number(aVal);
      const bNum = Number(bVal);

      if (!isNaN(aNum) && !isNaN(bNum)) {
        return aNum - bNum; // Ascending numeric sort
      }

      // Fall back to string comparison
      const aStr = String(aVal).toLowerCase();
      const bStr = String(bVal).toLowerCase();

      if (aStr < bStr) return -1;
      if (aStr > bStr) return 1;
      return 0;
    });
  }, [results]);

  // Show all data without pagination
  const allData = useMemo(() => {
    return sortedData;
  }, [sortedData]);

  // Reset column widths when results change
  React.useEffect(() => {
    setColumnWidths({}); // Reset column widths when results change
  }, [results]);

  // Sync scrolling between table and fixed scrollbar
  React.useEffect(() => {
    const tableElement = tableRef.current;
    const scrollbarElement = scrollbarRef.current;

    if (!tableElement || !scrollbarElement) return;

    const handleTableScroll = () => {
      scrollbarElement.scrollLeft = tableElement.scrollLeft;
    };

    tableElement.addEventListener('scroll', handleTableScroll);

    return () => {
      tableElement.removeEventListener('scroll', handleTableScroll);
    };
  }, []);

  // No sorting handler needed - fixed primary key sorting

  // Column resizing handlers
  const handleMouseDown = useCallback((e: React.MouseEvent, column: string) => {
    e.preventDefault();
    setIsResizing(column);
    resizeStartX.current = e.clientX;
    resizeStartWidth.current = columnWidths[column] || 150;

    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return;
      const diff = e.clientX - resizeStartX.current;
      const newWidth = Math.max(50, resizeStartWidth.current + diff);
      setColumnWidths(prev => ({ ...prev, [column]: newWidth }));
    };

    const handleMouseUp = () => {
      setIsResizing(null);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [isResizing, columnWidths]);

  const handleDoubleClick = useCallback((column: string) => {
    // Auto-fit column width based on content
    setColumnWidths(prev => ({ ...prev, [column]: 200 }));
  }, []);

  // Function to get row highlighting for first row only
  const getRowHighlighting = (rowIndex: number) => {
    if (rowIndex !== currentRowIndex) return ''; // Only highlight current row

    if (!sourceResults?.data || !destinationResults?.data) return '';

    const sourceRow = sourceResults.data[rowIndex];
    const destinationRow = destinationResults.data[rowIndex];

    if (!sourceRow || !destinationRow) return 'bg-red-100'; // Red if missing

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    return isMatch ? 'bg-green-100' : 'bg-red-100'; // Green if match, red if different
  };

  // Function to get match status for a row
  const getRowMatchStatus = (rowIndex: number) => {
    if (!sourceResults?.data || !destinationResults?.data) return 'missing';

    const sourceRow = sourceResults.data[rowIndex];
    const destinationRow = destinationResults.data[rowIndex];

    if (!sourceRow || !destinationRow) return 'missing';

    const isMatch = JSON.stringify(sourceRow) === JSON.stringify(destinationRow);
    return isMatch ? 'match' : 'mismatch';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-sm text-slate-600">Executing query...</p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <FileText className="w-12 h-12 mx-auto mb-4 text-slate-400" />
          <p className="text-sm text-slate-600">No results to display</p>
          <p className="text-xs text-slate-400 mt-1">Execute a query to see results</p>
        </div>
      </div>
    );
  }

  const columns = results.data.length > 0 ? Object.keys(results.data[0]) : [];
  const allColumns = ['Row Status', ...columns]; // Add Row Status column as first column

  return (
    <div className="flex flex-col h-full space-y-4 min-h-0">
      {/* Results Table */}
      <div className="flex-1 border border-slate-200 rounded-lg flex flex-col min-h-0 relative">
        {/* Table Container */}
        <div className="flex-1 min-h-0 relative">
          <div
            ref={tableRef}
            className="table-scroll-container h-full w-full"
            style={{
              scrollbarWidth: 'thin',
              scrollbarColor: '#cbd5e1 #f1f5f9',
              overflowY: 'auto',
              overflowX: 'auto', // Show horizontal scrollbar
              minHeight: '300px',
              maxHeight: '100%'
            }}
          >
            <Table className="w-full" style={{ minWidth: `${Math.max(allColumns.length * 150, 800)}px` }}>
              <TableHeader className="sticky top-0 z-10 bg-slate-100 border-b-2 border-slate-300">
                <TableRow>
                  {allColumns.map((column, index) => {
                    const isStatusColumn = column === 'Row Status';
                    const columnWidth = isStatusColumn ? 100 : (columnWidths[column] || (index === 1 ? 200 : 150));

                    return (
                      <TableHead
                        key={column}
                        className="font-semibold text-slate-800 border-r border-slate-200 last:border-r-0 px-4 py-3 text-left whitespace-nowrap bg-slate-100 relative group"
                        style={{
                          width: columnWidth,
                          minWidth: columnWidth,
                          maxWidth: columnWidth
                        }}
                      >
                        <div className="flex items-center justify-between pr-6">
                          <span className="truncate">{column}</span>
                          {!isStatusColumn && (
                            <div className="flex items-center ml-2">
                              {/* Sort Icon - disabled for detailed view */}
                              <ArrowUpDown className="w-4 h-4 text-slate-300" />
                            </div>
                          )}
                        </div>
                        {!isStatusColumn && (
                          /* Column Resize Handle */
                          <div
                            className="absolute right-0 top-0 bottom-0 w-1 cursor-col-resize bg-transparent hover:bg-blue-400 group-hover:bg-blue-300 transition-colors"
                            onMouseDown={(e) => {
                              e.stopPropagation();
                              handleMouseDown(e, column);
                            }}
                            onDoubleClick={(e) => {
                              e.stopPropagation();
                              handleDoubleClick(column);
                            }}
                            style={{
                              right: '-2px',
                              width: '4px',
                              zIndex: 20
                            }}
                            title="Drag to resize, double-click to auto-fit"
                          />
                        )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              </TableHeader>
              <TableBody>
                {allData.map((row, index) => {
                  const rowHighlighting = getRowHighlighting(index);
                  const matchStatus = getRowMatchStatus(index);

                  return (
                    <TableRow
                      key={index}
                      className={`table-row-optimized hover:bg-slate-50 transition-colors border-b border-slate-100 ${rowHighlighting}`}
                    >
                      {/* Row Status Indicator Column */}
                      <TableCell
                        className="border-r border-slate-100 text-sm px-4 py-2 whitespace-nowrap text-center"
                        style={{
                          width: 100,
                          minWidth: 100,
                          maxWidth: 100
                        }}
                      >
                        <div className="flex justify-center">
                          {matchStatus === 'match' && (
                            <div className="w-3 h-3 bg-green-500 rounded-full" title="Row matches"></div>
                          )}
                          {matchStatus === 'mismatch' && (
                            <div className="w-3 h-3 bg-red-500 rounded-full" title="Row differs"></div>
                          )}
                          {matchStatus === 'missing' && (
                            <div className="w-3 h-3 bg-gray-400 rounded-full" title="Row missing"></div>
                          )}
                        </div>
                      </TableCell>

                      {/* Data Columns */}
                      {columns.map((column, colIndex) => {
                        const cellValue = row[column];
                        const displayValue = cellValue !== null && cellValue !== undefined ? String(cellValue) : '';
                        const columnWidth = columnWidths[column] || (colIndex === 0 ? 200 : 150);

                        return (
                          <TableCell
                            key={column}
                            className="border-r border-slate-100 last:border-r-0 text-sm px-4 py-2 whitespace-nowrap overflow-hidden text-ellipsis"
                            style={{
                              width: columnWidth,
                              minWidth: columnWidth,
                              maxWidth: columnWidth
                            }}
                            title={displayValue} // Show full value on hover
                          >
                            <div className="truncate">
                              {displayValue}
                            </div>
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Fixed Horizontal Scrollbar - Always Visible */}
        <div
          ref={scrollbarRef}
          className="w-full bg-white border-t border-slate-200 z-20"
          style={{
            height: '17px',
            overflowX: 'scroll', // Always show horizontal scrollbar
            overflowY: 'hidden',
            scrollbarWidth: 'thin',
            scrollbarColor: '#cbd5e1 #f1f5f9'
          }}
          onScroll={(e) => {
            if (tableRef.current) {
              tableRef.current.scrollLeft = e.currentTarget.scrollLeft;
            }
          }}
        >
          <div style={{ width: `${Math.max(allColumns.length * 150, 800)}px`, height: '1px' }}></div>
        </div>
      </div>
    </div>
  );
};

export default DetailedResultsTable;
