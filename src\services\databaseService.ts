const API_BASE_URL = '/api';

export interface QueryResult {
  data: Record<string, any>[];
  totalRows: number;
  displayedRows?: number;
  isLimited?: boolean;
}

export interface DatabaseConnection {
  success: boolean;
  message: string;
  server?: string;
  data?: any;
  error?: string;
}

export interface ServerInfo {
  name: string;
  alias: string;
  user: string;
}

class DatabaseService {
  // Test connection to SQL Server
  async testConnection(): Promise<DatabaseConnection> {
    try {
      const response = await fetch(`${API_BASE_URL}/test-connection`);
      const data = await response.json();
      return data;
    } catch (error) {
      return {
        success: false,
        message: 'Failed to connect to server',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Get available databases
  async getDatabases(): Promise<string[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/databases`);
      const data = await response.json();
      
      if (data.success) {
        return data.databases;
      } else {
        throw new Error(data.message || 'Failed to get databases');
      }
    } catch (error) {
      console.error('Error fetching databases:', error);
      throw error;
    }
  }

  // Execute SQL query
  async executeQuery(database: string, query: string, limit?: number): Promise<QueryResult> {
    try {
      const requestBody: any = { database, query };

      // Add limit for performance (default to 100,000 rows max)
      if (limit !== undefined) {
        requestBody.limit = limit;
      } else {
        // Default limit to handle up to 100k records efficiently
        requestBody.limit = 100000;
      }

      const response = await fetch(`${API_BASE_URL}/execute-query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      const data = await response.json();

      if (data.success) {
        return {
          data: data.data,
          totalRows: data.totalRows,
          displayedRows: data.displayedRows,
          isLimited: data.isLimited
        };
      } else {
        throw new Error(data.message || 'Query execution failed');
      }
    } catch (error) {
      console.error('Error executing query:', error);
      throw error;
    }
  }

  // Get server information
  async getServerInfo(): Promise<ServerInfo> {
    try {
      const response = await fetch(`${API_BASE_URL}/server-info`);
      const data = await response.json();
      
      if (data.success) {
        return data.server;
      } else {
        throw new Error(data.message || 'Failed to get server info');
      }
    } catch (error) {
      console.error('Error fetching server info:', error);
      throw error;
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${API_BASE_URL}/health`);
      const data = await response.json();
      return data.success;
    } catch (error) {
      console.error('Health check failed:', error);
      return false;
    }
  }
}

export const databaseService = new DatabaseService();
