import React, { useMemo, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useQuery } from '../context/QueryContext';
import DetailedResultsTable from './DetailedResultsTable';

const DetailedReviewModal = () => {
  const {
    isDetailedReviewOpen,
    setIsDetailedReviewOpen,
    sourceResults,
    destinationResults,
    currentReviewRowIndex,
    setCurrentReviewRowIndex,
  } = useQuery();



  // Reset row index when modal opens
  useEffect(() => {
    if (isDetailedReviewOpen) {
      setCurrentReviewRowIndex(0);
    }
  }, [isDetailedReviewOpen, setCurrentReviewRowIndex]);

  // Navigation functions
  const handleNext = () => {
    const maxRows = Math.max(
      sourceResults?.data?.length || 0,
      destinationResults?.data?.length || 0
    );
    if (currentReviewRowIndex < maxRows - 1) {
      setCurrentReviewRowIndex(currentReviewRowIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentReviewRowIndex > 0) {
      setCurrentReviewRowIndex(currentReviewRowIndex - 1);
    }
  };

  // Keyboard navigation
  useEffect(() => {
    if (!isDetailedReviewOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          event.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
        case 'ArrowDown':
          event.preventDefault();
          handleNext();
          break;
        case 'Escape':
          event.preventDefault();
          handleClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isDetailedReviewOpen, currentReviewRowIndex, sourceResults, destinationResults]);

  const handleClose = () => {
    setIsDetailedReviewOpen(false);
  };

  if (!isDetailedReviewOpen) return null;

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center">
      {/* Modal Container with 2% margin */}
      <div
        className="bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden"
        style={{
          width: '96%',
          height: '90%',
          margin: '2% 2% 5% 2%'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-200 bg-gradient-to-r from-purple-50 via-pink-50 to-blue-50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Eye className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-slate-800">Detailed Review</h2>
              <p className="text-xs text-slate-600">Side-by-side comparison analysis</p>
            </div>
          </div>
          <Button
            onClick={handleClose}
            variant="ghost"
            size="sm"
            className="hover:bg-red-100 hover:text-red-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* Main Content - Full height */}
        <div className="flex-1 flex p-4" style={{ gap: '10px' }}>
          {/* Source Result Set - Exactly 50% width */}
          <div className="w-1/2 flex flex-col bg-blue-50 rounded-lg shadow-lg border-2 border-blue-300">
            <div className="p-4 border-b-2 border-blue-300 bg-gradient-to-r from-blue-100 to-blue-200">
              <h3 className="font-bold text-blue-900 text-lg">📊 Source Result Set</h3>
              <p className="text-sm text-blue-700 font-medium">{sourceResults?.data?.length || 0} rows available</p>
            </div>
            <div className="flex-1 min-h-0">
              <DetailedResultsTable
                results={sourceResults}
                loading={false}
                type="source"
                currentRowIndex={currentReviewRowIndex}
              />
            </div>
          </div>

          {/* Destination Result Set - Exactly 50% width */}
          <div className="w-1/2 flex flex-col bg-green-50 rounded-lg shadow-lg border-2 border-green-300">
            <div className="p-4 border-b-2 border-green-300 bg-gradient-to-r from-green-100 to-green-200">
              <h3 className="font-bold text-green-900 text-lg">🎯 Destination Result Set</h3>
              <p className="text-sm text-green-700 font-medium">{destinationResults?.data?.length || 0} rows available</p>
            </div>
            <div className="flex-1 min-h-0">
              <DetailedResultsTable
                results={destinationResults}
                loading={false}
                type="destination"
                currentRowIndex={currentReviewRowIndex}
              />
            </div>
          </div>
        </div>

        {/* Navigation Controls */}
        <div className="flex items-center justify-center gap-6 p-4 bg-gradient-to-r from-slate-50 via-blue-50 to-purple-50 border-t border-slate-200">
          <Button
            onClick={handlePrevious}
            disabled={currentReviewRowIndex === 0}
            className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white disabled:opacity-50 disabled:transform-none transform hover:scale-105 transition-all duration-200 px-6 py-3 rounded-xl shadow-lg"
          >
            <ChevronLeft className="w-5 h-5" />
            Previous
          </Button>

          <div className="text-center bg-white/70 backdrop-blur-sm rounded-xl p-4 border border-white/50 shadow-md">
            <div className="text-lg font-bold text-slate-800">
              Row {currentReviewRowIndex + 1} of {Math.max(
                sourceResults?.data?.length || 0,
                destinationResults?.data?.length || 0
              )}
            </div>
            <div className="text-sm text-slate-600 mt-1">
              Navigate through synchronized result sets
            </div>
          </div>

          <Button
            onClick={handleNext}
            disabled={currentReviewRowIndex >= Math.max(
              (sourceResults?.data?.length || 0) - 1,
              (destinationResults?.data?.length || 0) - 1
            )}
            className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white disabled:opacity-50 disabled:transform-none transform hover:scale-105 transition-all duration-200 px-6 py-3 rounded-xl shadow-lg"
          >
            Next
            <ChevronRight className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DetailedReviewModal;
