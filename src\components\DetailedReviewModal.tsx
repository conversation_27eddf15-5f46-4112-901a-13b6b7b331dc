import React, { useMemo, useEffect } from 'react';
import { X, ChevronLeft, ChevronRight, Eye } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useQuery } from '../context/QueryContext';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const DetailedReviewModal = () => {
  const {
    isDetailedReviewOpen,
    setIsDetailedReviewOpen,
    sourceResults,
    destinationResults,
    currentReviewRowIndex,
    setCurrentReviewRowIndex,
  } = useQuery();

  // Sort data by primary key (assuming first column is primary key)
  const sortedSourceData = useMemo(() => {
    if (!sourceResults?.data) return [];
    return [...sourceResults.data].sort((a, b) => {
      const firstKey = Object.keys(a)[0];
      const aVal = a[firstKey];
      const bVal = b[firstKey];
      if (aVal < bVal) return -1;
      if (aVal > bVal) return 1;
      return 0;
    });
  }, [sourceResults]);

  const sortedDestinationData = useMemo(() => {
    if (!destinationResults?.data) return [];
    return [...destinationResults.data].sort((a, b) => {
      const firstKey = Object.keys(a)[0];
      const aVal = a[firstKey];
      const bVal = b[firstKey];
      if (aVal < bVal) return -1;
      if (aVal > bVal) return 1;
      return 0;
    });
  }, [destinationResults]);

  const maxRows = Math.max(sortedSourceData.length, sortedDestinationData.length);

  // Reset row index when modal opens
  useEffect(() => {
    if (isDetailedReviewOpen) {
      setCurrentReviewRowIndex(0);
    }
  }, [isDetailedReviewOpen, setCurrentReviewRowIndex]);

  // Keyboard navigation
  useEffect(() => {
    if (!isDetailedReviewOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      switch (event.key) {
        case 'ArrowLeft':
        case 'ArrowUp':
          event.preventDefault();
          handlePrevious();
          break;
        case 'ArrowRight':
        case 'ArrowDown':
          event.preventDefault();
          handleNext();
          break;
        case 'Escape':
          event.preventDefault();
          handleClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isDetailedReviewOpen, currentReviewRowIndex, maxRows]);

  const handleNext = () => {
    if (currentReviewRowIndex < maxRows - 1) {
      setCurrentReviewRowIndex(currentReviewRowIndex + 1);
    }
  };

  const handlePrevious = () => {
    if (currentReviewRowIndex > 0) {
      setCurrentReviewRowIndex(currentReviewRowIndex - 1);
    }
  };

  const handleClose = () => {
    setIsDetailedReviewOpen(false);
  };

  if (!isDetailedReviewOpen) return null;

  const sourceColumns = sortedSourceData.length > 0 ? Object.keys(sortedSourceData[0]) : [];
  const destinationColumns = sortedDestinationData.length > 0 ? Object.keys(sortedDestinationData[0]) : [];

  const currentSourceRow = sortedSourceData[currentReviewRowIndex];
  const currentDestinationRow = sortedDestinationData[currentReviewRowIndex];

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm flex items-center justify-center">
      {/* Modal Container with 5% margin */}
      <div 
        className="bg-white rounded-xl shadow-2xl flex flex-col overflow-hidden"
        style={{
          width: '90%',
          height: '90%',
          margin: '5%'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-200 bg-gradient-to-r from-purple-50 via-pink-50 to-blue-50">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg">
              <Eye className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-slate-800">Detailed Review</h2>
              <p className="text-xs text-slate-600">Row-by-row comparison analysis</p>
            </div>
            <span className="text-sm text-slate-700 bg-white/70 backdrop-blur-sm px-3 py-1 rounded-full border border-slate-200 shadow-sm">
              Row {currentReviewRowIndex + 1} of {maxRows}
            </span>
          </div>
          <Button
            onClick={handleClose}
            variant="ghost"
            size="sm"
            className="hover:bg-red-100 hover:text-red-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* First Row - 80% height */}
        <div className="flex-1 flex p-4" style={{ height: '80%', gap: '10px' }}>
          {/* Source Result Set - Exactly 50% width */}
          <div className="w-1/2 flex flex-col bg-blue-50 rounded-lg shadow-lg border-2 border-blue-300">
            <div className="p-4 border-b-2 border-blue-300 bg-gradient-to-r from-blue-100 to-blue-200">
              <h3 className="font-bold text-blue-900 text-lg">📊 Source Result Set</h3>
              <p className="text-sm text-blue-700 font-medium">{sortedSourceData.length} rows available</p>
            </div>
            <div className="flex-1 overflow-auto">
              {sourceColumns.length > 0 ? (
                <Table className="table-fixed w-full">
                  <TableHeader className="sticky top-0 bg-blue-100">
                    <TableRow>
                      {sourceColumns.map((column) => (
                        <TableHead key={column} className="font-semibold text-blue-800 border-r border-blue-200 last:border-r-0 whitespace-normal break-words max-w-xs p-3">
                          <div className="break-words overflow-wrap-anywhere">
                            {column}
                          </div>
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedSourceData.map((row, index) => (
                      <TableRow
                        key={index}
                        className={`${
                          index === currentReviewRowIndex
                            ? 'bg-blue-200 border-blue-400 border-2 shadow-md transform scale-[1.01]'
                            : 'hover:bg-blue-50'
                        } transition-all duration-200`}
                      >
                        {sourceColumns.map((column) => (
                          <TableCell key={column} className="border-r border-blue-100 last:border-r-0 whitespace-normal break-words max-w-xs p-3">
                            <div className="break-words overflow-wrap-anywhere">
                              {row[column] !== null && row[column] !== undefined ? String(row[column]) : ''}
                            </div>
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex items-center justify-center h-full text-blue-600">
                  <div className="text-center">
                    <div className="text-lg font-semibold">No Source Data</div>
                    <div className="text-sm">Execute source query to view results</div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Destination Result Set - Exactly 50% width */}
          <div className="w-1/2 flex flex-col bg-green-50 rounded-lg shadow-lg border-2 border-green-300">
            <div className="p-4 border-b-2 border-green-300 bg-gradient-to-r from-green-100 to-green-200">
              <h3 className="font-bold text-green-900 text-lg">🎯 Destination Result Set</h3>
              <p className="text-sm text-green-700 font-medium">{sortedDestinationData.length} rows available</p>
            </div>
            <div className="flex-1 overflow-auto">
              {destinationColumns.length > 0 ? (
                <Table className="table-fixed w-full">
                  <TableHeader className="sticky top-0 bg-green-100">
                    <TableRow>
                      {destinationColumns.map((column) => (
                        <TableHead key={column} className="font-semibold text-green-800 border-r border-green-200 last:border-r-0 whitespace-normal break-words max-w-xs p-3">
                          <div className="break-words overflow-wrap-anywhere">
                            {column}
                          </div>
                        </TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedDestinationData.map((row, index) => (
                      <TableRow
                        key={index}
                        className={`${
                          index === currentReviewRowIndex
                            ? 'bg-green-200 border-green-400 border-2 shadow-md transform scale-[1.01]'
                            : 'hover:bg-green-50'
                        } transition-all duration-200`}
                      >
                        {destinationColumns.map((column) => (
                          <TableCell key={column} className="border-r border-green-100 last:border-r-0 whitespace-normal break-words max-w-xs p-3">
                            <div className="break-words overflow-wrap-anywhere">
                              {row[column] !== null && row[column] !== undefined ? String(row[column]) : ''}
                            </div>
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex items-center justify-center h-full text-green-600">
                  <div className="text-center">
                    <div className="text-lg font-semibold">No Destination Data</div>
                    <div className="text-sm">Execute destination query to view results</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Second Row - 20% height - Navigation Controls */}
        <div className="bg-gradient-to-r from-slate-50 via-purple-50 to-pink-50 border-t border-slate-200 p-6 flex items-center justify-center gap-6" style={{ height: '20%' }}>
          <Button
            onClick={handlePrevious}
            disabled={currentReviewRowIndex === 0}
            className="flex items-center gap-2 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white disabled:opacity-50 disabled:transform-none transform hover:scale-105 transition-all duration-200 px-6 py-3 rounded-xl shadow-lg"
          >
            <ChevronLeft className="w-5 h-5" />
            Previous
          </Button>

          <div className="text-center bg-white/60 backdrop-blur-sm rounded-xl p-4 border border-white/50 shadow-md">
            <div className="text-xl font-bold text-slate-800">
              Row {currentReviewRowIndex + 1} of {maxRows}
            </div>
            <div className="text-sm text-slate-600 mt-1">
              Navigate through synchronized result sets
            </div>
            <div className="flex items-center justify-center gap-2 mt-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span className="text-xs text-slate-500">Source</span>
              <div className="w-2 h-2 bg-green-500 rounded-full ml-3"></div>
              <span className="text-xs text-slate-500">Destination</span>
            </div>
            <div className="text-xs text-slate-500 mt-2 opacity-75">
              Use arrow keys or buttons to navigate • ESC to close
            </div>
          </div>

          <Button
            onClick={handleNext}
            disabled={currentReviewRowIndex === maxRows - 1}
            className="flex items-center gap-2 bg-gradient-to-r from-slate-600 to-slate-700 hover:from-slate-700 hover:to-slate-800 text-white disabled:opacity-50 disabled:transform-none transform hover:scale-105 transition-all duration-200 px-6 py-3 rounded-xl shadow-lg"
          >
            Next
            <ChevronRight className="w-5 h-5" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DetailedReviewModal;
