@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar styles for better visibility */
@layer utilities {
  /* Always show horizontal scrollbar */
  .scrollbar-always-visible {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .scrollbar-always-visible::-webkit-scrollbar {
    height: 12px;
    width: 12px;
  }

  .scrollbar-always-visible::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 6px;
  }

  .scrollbar-always-visible::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 6px;
    border: 2px solid #f1f5f9;
  }

  .scrollbar-always-visible::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Force horizontal scrollbar to always be visible */
  .force-horizontal-scrollbar {
    overflow-x: scroll !important;
  }

  /* Default table scroll container - hide horizontal scrollbar */
  .table-scroll-container {
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .table-scroll-container::-webkit-scrollbar {
    height: 14px;
    width: 14px;
  }

  .table-scroll-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 7px;
  }

  .table-scroll-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 7px;
    border: 2px solid #f1f5f9;
  }

  .table-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Result set table containers - show both scrollbars */
  .result-table-scroll-container {
    overflow-y: auto;
    overflow-x: hidden; /* Hide native horizontal scrollbar, use fixed one below */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }

  .result-table-scroll-container::-webkit-scrollbar {
    height: 14px;
    width: 14px;
  }

  .result-table-scroll-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 7px;
  }

  .result-table-scroll-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 7px;
    border: 2px solid #f1f5f9;
  }

  .result-table-scroll-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Performance optimizations for large datasets */
  .table-row-optimized {
    contain: layout style paint;
    will-change: transform;
  }

  /* Sorting indicators */
  .sort-icon-active {
    color: #2563eb;
  }

  .sort-icon-inactive {
    color: #94a3b8;
    transition: color 0.2s ease;
  }

  .sort-icon-inactive:hover {
    color: #64748b;
  }

  /* Table cell text handling */
  .table-cell-no-wrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Sticky elements with proper z-index */
  .sticky-header {
    position: sticky;
    top: 0;
    z-index: 20;
    background: white;
    border-bottom: 1px solid #e2e8f0;
  }

  .sticky-pagination {
    position: sticky;
    top: 120px;
    z-index: 15;
    background: white;
    border-bottom: 1px solid #e2e8f0;
  }

  .sticky-scrollbar {
    position: sticky;
    bottom: 0;
    z-index: 10;
    background: white;
    border-top: 1px solid #e2e8f0;
  }

  /* Column resize cursor */
  .col-resize-cursor {
    cursor: col-resize !important;
  }

  /* Prevent text selection during resize */
  .no-select {
    user-select: none !important;
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
  }

  /* Table header improvements */
  .table-header-cell {
    position: relative;
    background: #f8fafc;
    border-right: 1px solid #e2e8f0;
  }

  .table-header-cell:last-child {
    border-right: none;
  }

  /* Resize handle styling */
  .resize-handle {
    position: absolute;
    right: -2px;
    top: 0;
    bottom: 0;
    width: 4px;
    cursor: col-resize;
    background: transparent;
    transition: background-color 0.2s;
  }

  .resize-handle:hover {
    background: #3b82f6;
  }

  /* Table container improvements */
  .table-container {
    position: relative;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
  }
}